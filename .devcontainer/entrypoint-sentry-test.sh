#!/bin/bash
set -e

echo "Setting up sentry-test environment..."

# Ensure proper ownership of workspace (excluding mounted volumes)
chown sentry:sentry /workspace/sentry
find /workspace/sentry -maxdepth 1 -type f -exec chown sentry:sentry {} \;
find /workspace/sentry -maxdepth 1 -type d ! -path "/workspace/sentry/log" -exec chown sentry:sentry {} \;
# Recursively chown directories that are not mounted volumes
for dir in /workspace/sentry/*/; do
  if [ -d "$dir" ] && [ "$(basename "$dir")" != "log" ]; then
    chown -R sentry:sentry "$dir"
  fi
done

# Switch to sentry user and run bundle install
echo "Installing bundle dependencies in root folder..."
su - sentry -c "cd /workspace/sentry && bundle install"

echo "✅ sentry-test setup completed!"

# Switch to sentry user for the main command
exec su - sentry -c "cd /workspace/sentry && exec $*"
