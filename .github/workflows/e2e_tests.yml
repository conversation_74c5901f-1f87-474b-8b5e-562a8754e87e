name: e2e tests

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:

concurrency:
  group: e2e-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  e2e-tests:
    name: e2e tests
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      fail-fast: false
      matrix:
        ruby_version: ["3.4.5"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up `.env` file
        run: |
          cd .devcontainer
          cp .env.example .env

      - name: Start mini rails app
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e up -d sentry-rails-mini

      - name: Start mini svelte app
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e up -d sentry-svelte-mini

      - name: Run e2e tests
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e run --rm sentry-test bundle exec rake
        env:
          SENTRY_E2E_RAILS_APP_URL: http://rails-mini:5000
          SENTRY_E2E_SVELTE_APP_URL: http://svelte-mini:5001

      - name: Stop e2e services
        if: always()
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e down

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-logs-ruby-${{ matrix.ruby_version }}
          path: |
            spec/apps/rails-mini/log/sentry_debug_events.log
          retention-days: 7
